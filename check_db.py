#!/usr/bin/env python3
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))
from extensions.ext_database import db
from models.provider import ProviderModel, TenantDefaultModel
from models.account import Tenant
from sqlalchemy import text

# 查询租户信息
print('=== 租户信息 ===')
tenants = db.session.execute(text('SELECT id, name FROM tenants LIMIT 5')).fetchall()
for tenant in tenants:
    print(f'Tenant ID: {tenant[0]}, Name: {tenant[1]}')

print('\n=== Provider Models ===')
# 查询ai_gateway_platform相关的模型
provider_models = db.session.execute(text(
    "SELECT tenant_id, provider_name, model_name, model_type, is_valid FROM provider_models WHERE provider_name = 'ai_gateway_platform' ORDER BY created_at DESC LIMIT 10"
)).fetchall()
for pm in provider_models:
    print(f'Tenant: {pm[0]}, Provider: {pm[1]}, Model: {pm[2]}, Type: {pm[3]}, Valid: {pm[4]}')

print('\n=== Tenant Default Models ===')
# 查询默认模型
default_models = db.session.execute(text(
    "SELECT tenant_id, provider_name, model_name, model_type FROM tenant_default_models WHERE model_type = 'llm' ORDER BY created_at DESC LIMIT 10"
)).fetchall()
for dm in default_models:
    print(f'Tenant: {dm[0]}, Provider: {dm[1]}, Model: {dm[2]}, Type: {dm[3]}')

print('\n=== 所有Provider Models统计 ===')
provider_stats = db.session.execute(text(
    "SELECT provider_name, COUNT(id) as count FROM provider_models GROUP BY provider_name ORDER BY count DESC"
)).fetchall()
for stat in provider_stats:
    print(f'Provider: {stat[0]}, Count: {stat[1]}')

print('\n=== 检查最近的provider_models记录 ===')
recent_models = db.session.execute(text(
    "SELECT tenant_id, provider_name, model_name, model_type, is_valid, created_at FROM provider_models ORDER BY created_at DESC LIMIT 10"
)).fetchall()
for rm in recent_models:
    print(f'Tenant: {rm[0]}, Provider: {rm[1]}, Model: {rm[2]}, Type: {rm[3]}, Valid: {rm[4]}, Created: {rm[5]}')
